// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-sqlite')
    implementation project(':capacitor-app')
    implementation project(':capacitor-device')
    implementation project(':capacitor-file-transfer')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-network')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-toast')
    implementation project(':capawesome-team-capacitor-file-opener')
    implementation project(':capawesome-capacitor-file-picker')
    implementation project(':capgo-capacitor-social-login')
    implementation project(':capacitor-secure-storage-plugin')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
