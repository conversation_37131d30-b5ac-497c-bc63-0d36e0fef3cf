[{"pkg": "@capacitor-community/sqlite", "classpath": "com.getcapacitor.community.database.sqlite.CapacitorSQLitePlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/file-transfer", "classpath": "com.capacitorjs.plugins.filetransfer.FileTransferPlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/network", "classpath": "com.capacitorjs.plugins.network.NetworkPlugin"}, {"pkg": "@capacitor/preferences", "classpath": "com.capacitorjs.plugins.preferences.PreferencesPlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@capacitor/toast", "classpath": "com.capacitorjs.plugins.toast.ToastPlugin"}, {"pkg": "@capawesome-team/capacitor-file-opener", "classpath": "io.capawesome.capacitorjs.plugins.fileopener.FileOpenerPlugin"}, {"pkg": "@capawesome/capacitor-file-picker", "classpath": "io.capawesome.capacitorjs.plugins.filepicker.FilePickerPlugin"}, {"pkg": "@capgo/capacitor-social-login", "classpath": "ee.forgr.capacitor.social.login.SocialLoginPlugin"}, {"pkg": "capacitor-secure-storage-plugin", "classpath": "com.whitestein.securestorage.SecureStoragePluginPlugin"}]