{"name": "spendwise", "private": true, "version": "1.2.0", "versionCode": 1, "author": "ALR2310", "license": "UNLICENSED", "description": "Ứng dụng ghi chú và quản lý chi tiêu cá nhân", "type": "module", "scripts": {"dev": "vite --host", "dev:android": "vite build && npx cap sync && npx cap run android", "dev:android-live": "vite build && npx cap sync && npx cap run android --live-reload --port=8100 --host=************", "preview": "vite preview", "lint": "eslint", "update": "npm-check-updates -u", "version": "tsx scripts/version.ts", "build": "tsc && vite build", "build:image": "nodemon --exec tsx scripts/build-image.ts --watch src/assets/imgs --ext png,jpg,jpeg,svg,gif", "build:android": "vite build && npx cap sync && npx cap build android"}, "dependencies": {"@capacitor-community/sqlite": "^7.0.1", "@capacitor/android": "7.4.2", "@capacitor/app": "7.0.1", "@capacitor/core": "7.4.2", "@capacitor/device": "^7.0.1", "@capacitor/file-transfer": "^1.0.2", "@capacitor/filesystem": "^7.1.3", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@capacitor/toast": "^7.0.1", "@capawesome-team/capacitor-file-opener": "^7.0.1", "@capawesome/capacitor-file-picker": "^7.2.0", "@capgo/capacitor-social-login": "^7.8.3", "@tanstack/react-query": "^5.83.0", "@types/react-router": "^5.1.20", "animate.css": "^4.1.1", "capacitor-secure-storage-plugin": "^0.11.0", "compare-versions": "^6.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "downloadjs": "^1.4.7", "i18next": "^25.3.2", "jeep-sqlite": "^2.8.0", "lodash": "^4.17.21", "motion": "^12.23.11", "pako": "^2.1.0", "react": "19.1.1", "react-datepicker": "^8.4.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.1", "react-i18next": "^15.6.1", "react-router": "^7.7.1", "react-router-dom": "^7.7.1", "reflect-metadata": "^0.2.2", "sortablejs": "^1.15.6"}, "devDependencies": {"@capacitor/cli": "7.4.2", "@eslint/js": "^9.32.0", "@tailwindcss/vite": "^4.1.11", "@types/lodash": "^4.17.20", "@types/pako": "^2.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-react": "^4.7.0", "daisyui": "5.0.50", "dotenv": "^17.2.1", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.3.0", "jsdom": "^26.1.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite": "~7.0.6", "vite-plugin-static-copy": "^3.1.1"}}