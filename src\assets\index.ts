import icons_cloudSync from '~/assets/imgs/icons/cloudSync.png';
import icons_cloudUpload from '~/assets/imgs/icons/cloudUpload.png';
import icons_data from '~/assets/imgs/icons/data.png';
import icons_dataClean from '~/assets/imgs/icons/dataClean.png';
import icons_exportFile from '~/assets/imgs/icons/exportFile.png';
import icons_google from '~/assets/imgs/icons/google.png';
import icons_importFile from '~/assets/imgs/icons/importFile.png';
import icons_language from '~/assets/imgs/icons/language.png';
import icons_notify from '~/assets/imgs/icons/notify.png';
import icons_page from '~/assets/imgs/icons/page.png';
import icons_theme from '~/assets/imgs/icons/theme.png';
import icons_update from '~/assets/imgs/icons/update.png';
import logos_app from '~/assets/imgs/logos/app.png';

export const appImages = {
  icons: {
    cloudSync: icons_cloudSync,
    cloudUpload: icons_cloudUpload,
    data: icons_data,
    dataClean: icons_dataClean,
    exportFile: icons_exportFile,
    google: icons_google,
    importFile: icons_importFile,
    language: icons_language,
    notify: icons_notify,
    page: icons_page,
    theme: icons_theme,
    update: icons_update
  },
  logos: {
    app: logos_app
  }
};
