import { W as WebPlugin } from "./index-CmwUdWjt.js";
class x extends WebPlugin {
  constructor() {
    super(...arguments), this.lastProgressUpdate = 0, this.PROGRESS_UPDATE_INTERVAL = 100;
  }
  // 100ms between progress updates
  async downloadFile(e) {
    try {
      const t = this.buildUrl(e.url, e.params), s = new Headers(e.headers), a = {
        method: e.method || "GET",
        headers: s,
        redirect: e.disableRedirects ? "manual" : "follow"
      }, r = new AbortController(), n = e.connectTimeout || 6e4, i = setTimeout(() => r.abort(), n);
      let l, o = 0;
      try {
        const h = await fetch(t, {
          ...a,
          signal: r.signal
        });
        if (!h.ok)
          throw new Error(`HTTP error! status: ${h.status}`);
        if (e.progress && h.body) {
          const F = h.body.getReader(), y = parseInt(
            h.headers.get("content-length") || "0",
            10
          ), P = h.headers.get("content-type") || "";
          let m = 0;
          const g = [];
          for (; ; ) {
            const { done: p, value: f } = await F.read();
            if (p)
              break;
            f && (g.push(f), m += f.length, this.notifyProgress(
              e.url,
              m,
              y,
              y > 0,
              "download"
            ));
          }
          const b = new Uint8Array(m);
          let w = 0;
          for (const p of g)
            b.set(p, w), w += p.length;
          l = new Blob([b], { type: P }), o = m, e.progress && this.notifyProgress(
            e.url,
            o,
            o,
            // Set content length equal to bytes for 100%
            true,
            "download",
            true
            // Force update
          );
        } else
          l = await h.blob(), o = l.size;
      } finally {
        clearTimeout(i);
      }
      if (await this.tryWriteToFilesystem(
        l,
        e.path
      ))
        return {
          path: e.path,
          blob: l
        };
      const u = URL.createObjectURL(l), c = document.createElement("a");
      return c.href = u, c.download = this.extractFilename(e.path, e.url), document.body.appendChild(c), c.click(), document.body.removeChild(c), URL.revokeObjectURL(u), {
        blob: l,
        path: e.path
      };
    } catch (t) {
      throw this.handleError(t, e.url, e.path);
    }
  }
  async uploadFile(e) {
    try {
      let t;
      if (e.blob)
        t = e.blob;
      else {
        const s = await this.tryReadFromFilesystem(e.path);
        if (s)
          t = s;
        else
          throw new Error(
            "File upload from path is not supported in web without @capacitor/filesystem plugin. Please provide a blob instead."
          );
      }
      return new Promise((s, a) => {
        const r = new XMLHttpRequest(), n = this.buildUrl(e.url, e.params);
        e.progress && (r.upload.onprogress = (o) => {
          this.notifyProgress(
            e.url,
            o.loaded,
            o.total,
            o.lengthComputable,
            "upload"
          );
        }), r.onload = () => {
          if (r.status >= 200 && r.status < 300) {
            e.progress && this.notifyProgress(
              e.url,
              t.size,
              // Total bytes
              t.size,
              // Same for content length to show 100%
              true,
              "upload",
              true
              // Force update
            );
            const o = {};
            r.getAllResponseHeaders().split(`\r
`).forEach((d) => {
              const [u, c] = d.split(": ");
              u && c && (o[u] = c);
            }), s({
              bytesSent: t.size,
              responseCode: r.status.toString(),
              response: r.responseText,
              headers: o
            });
          } else
            a(new Error(`HTTP error! status: ${r.status}`));
        }, r.onerror = () => {
          a(new Error("Network error occurred"));
        }, r.open(e.method || "POST", n, true), e.headers && Object.entries(e.headers).forEach(([o, d]) => {
          r.setRequestHeader(o, d);
        });
        const i = new FormData(), l = e.fileKey || "file";
        i.append(l, t, e.path), e.params && Object.entries(e.params).forEach(([o, d]) => {
          Array.isArray(d) ? d.forEach((u) => i.append(o, u)) : i.append(o, d);
        }), r.send(i);
      });
    } catch (t) {
      throw this.handleError(t, e.path, e.url);
    }
  }
  buildUrl(e, t) {
    if (!t || Object.keys(t).length === 0)
      return e;
    const s = new URL(e);
    return Object.entries(t).forEach(([a, r]) => {
      Array.isArray(r) ? r.forEach((n) => s.searchParams.append(a, n)) : s.searchParams.append(a, r);
    }), s.toString();
  }
  notifyProgress(e, t, s, a, r, n = false) {
    const i = Date.now();
    if (n || i - this.lastProgressUpdate >= this.PROGRESS_UPDATE_INTERVAL) {
      const l = {
        type: r,
        url: e,
        bytes: t,
        contentLength: s,
        lengthComputable: a
      };
      this.notifyListeners("progress", l), this.lastProgressUpdate = i;
    }
  }
  handleError(e, t, s) {
    return e instanceof TypeError && e.message === "Failed to fetch" ? {
      code: "OS-PLUG-FLTR-0008",
      message: "Failed to connect to server",
      source: t,
      target: s
    } : e instanceof Error ? {
      code: "OS-PLUG-FLTR-0011",
      message: e.message,
      source: t,
      target: s
    } : {
      code: "OS-PLUG-FLTR-0011",
      message: "An unknown error occurred",
      source: t,
      target: s
    };
  }
  extractFilename(e, t) {
    const a = e.split("?")[0].split("#")[0].split(/[/\\]/);
    let r = a[a.length - 1];
    if (!r.includes(".")) {
      const n = t.split(".");
      if (n.length > 1) {
        const i = n[n.length - 1].split("?")[0];
        r = `${n[n.length - 2].split("/").pop()}.${i}`;
      }
    }
    return r || (r = "download"), r;
  }
  /**
   * Checks if the Capacitor Filesystem plugin is available
   */
  isFilesystemAvailable() {
    try {
      return !!globalThis?.Capacitor?.Plugins?.Filesystem;
    } catch {
      return false;
    }
  }
  /**
   * Attempts to read a file using the Filesystem plugin if available
   * @param path Path to the file
   * @returns Blob with file contents or null if Filesystem plugin is not available
   */
  async tryReadFromFilesystem(e) {
    if (!this.isFilesystemAvailable())
      return null;
    try {
      const t = window.Capacitor?.Plugins?.Filesystem;
      if (!t)
        return null;
      const a = (await t.readFile({
        path: e
      })).data;
      if (!a)
        throw new Error("No data returned from Filesystem plugin");
      const r = this.getMimeTypeFromPath(e) || "application/octet-stream", n = atob(a), i = [];
      for (let l = 0; l < n.length; l += 512) {
        const o = n.slice(l, l + 512), d = new Array(o.length);
        for (let c = 0; c < o.length; c++)
          d[c] = o.charCodeAt(c);
        const u = new Uint8Array(d);
        i.push(u);
      }
      return new Blob(i, { type: r });
    } catch (t) {
      return console.error("Error reading file from Filesystem:", t), null;
    }
  }
  /**
   * Attempts to write a blob to a file using the Filesystem plugin if available
   * @param blob Blob data to write
   * @param path Path to write the file to
   * @returns true if the file was written successfully, false if Filesystem plugin is not available
   */
  async tryWriteToFilesystem(e, t) {
    if (!this.isFilesystemAvailable())
      return false;
    try {
      const s = window.Capacitor?.Plugins?.Filesystem;
      if (!s)
        return false;
      const a = await this.blobToBase64(e);
      if (!a)
        throw new Error("Failed to convert blob to base64");
      const r = t.split("/");
      if (r.length > 1) {
        const n = r.slice(0, -1).join("/");
        await s.mkdir({
          path: n,
          recursive: true
        });
      }
      return await s.writeFile({
        path: t,
        data: a.split(",")[1],
        // Remove the data:application/octet-stream;base64, part
        recursive: true
      }), true;
    } catch (s) {
      return console.error("Error writing file to Filesystem:", s), false;
    }
  }
  /**
   * Converts a Blob to a base64 string
   * @param blob The blob to convert
   * @returns Promise that resolves to the base64 string
   */
  blobToBase64(e) {
    return new Promise((t, s) => {
      const a = new FileReader();
      a.onload = () => t(a.result), a.onerror = () => s(new Error("Failed to convert blob to base64")), a.readAsDataURL(e);
    });
  }
  /**
   * Gets MIME type from file path based on extension
   * @param path File path
   * @returns MIME type string or null if unable to determine
   */
  getMimeTypeFromPath(e) {
    const t = e.split(".").pop()?.toLowerCase();
    return t && {
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      png: "image/png",
      gif: "image/gif",
      pdf: "application/pdf",
      txt: "text/plain",
      html: "text/html",
      htm: "text/html",
      json: "application/json",
      doc: "application/msword",
      docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      xls: "application/vnd.ms-excel",
      xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      zip: "application/zip",
      mp3: "audio/mpeg",
      mp4: "video/mp4",
      wav: "audio/wav",
      xml: "application/xml",
      csv: "text/csv"
    }[t] || null;
  }
}
export {
  x as FileTransferWeb
};
